// import type { IPaginationResponse, ICommonResponse } from './axios'
// import type { IAllRecord } from './record'

interface XTPT{
    //域名
    cDate?: string //底部时间 
    cName?: string //底部公司名
    companyName?: string //左侧顶部文字
    loginLogo?:string //登录页左上角的logo
    logoImg?: string //登录页图片
    TCP?: string //底部备案号
    logoBk?: string //首页上方logo
    //网页页签标签
    helpUrl?: string //帮助手册地址
}

interface OEMUploadModule {
    productType: 0 | 1 | 2 | 3
    config?: XTPT
}

export interface OEMUploadItem {
    key: string //渠道名
    modules: OEMUploadModule[]
}

// {
//   "key": "1",
//   "modules": [
//     {
//       "productType": 0,
//       "config": {
//         "companyName": "nanf"
//       }
//     }
//   ]
// }


// {
//   "bsonType": "object",
//   "required": [
//     "key"
//   ],
//   "permission": {
//     "read": true,
//     "create": true,
//     "update": true,
//     "delete": true
//   },
//   "properties": {
//     "_id": {
//       "description": "ID，系统自动生成"
//     },
//     "bannerDesc": {
//       "title": "首页的banner下部内容",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "首页的banner下部内容"
//     },
//     "bannerImg": {
//       "title": "首页的banner图",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "首页的banner图"
//     },
//     "bannerTxt": {
//       "title": "首页的banner上方大文字",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "首页的banner上方大文字"
//     },
//     "cDate": {
//       "title": "登录页下方的时间",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "登录页下方的时间"
//     },
//     "cName": {
//       "title": "登录页下方的公司名",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "登录页下方的公司名"
//     },
//     "companyName": {
//       "title": "登录页用户名上方的文字",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "登录页用户名上方的文字"
//     },
//     "key": {
//       "title": "渠道名",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "errorMessage": {
//         "required": "渠道名 不能为空"
//       },
//       "description": "渠道名"
//     },
//     "loginLogo": {
//       "title": "登录页左上角的logo",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "登录页左上角的logo"
//     },
//     "logoBk": {
//       "title": "首页的上方的logo",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "首页的上方的logo"
//     },
//     "logoImg": {
//       "title": "登录页图片",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "登录页图片"
//     },
//     "mpLogo": {
//       "title": "小程序首页logo",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "小程序首页logo"
//     },
//     "mpHomeBanner": {
//       "title": "小程序首页顶部图",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "小程序首页顶部图"
//     },
//     "mpQrCode": {
//       "title": "小程序底部二维码",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "小程序底部二维码"
//     },
//     "mpHomeSpec": {
//       "title": "小程序首页搜索框下方",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "小程序首页搜索框下方"
//     },
//     "mpTjLink": {
//       "title": "小程序立即体检跳转的链接",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "小程序立即体检跳转的链接"
//     },
//     "mpQrcodeText": {
//       "title": "小程序二维码名称",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "小程序二维码名称"
//     },
//     "logoShuzuColor": {
//       "title": "登录页下方的文字颜色",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "登录页下方的文字颜色"
//     },
//     "dashboardTitle": {
//       "title": "数据大屏标题",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "数据大屏标题"
//     },
//     "helpUrl": {
//       "title": "帮助手册地址",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "帮助手册地址"
//     },
//     "h5Title": {
//       "title": "页签标题",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "页签标题"
//     },
//     "TCP": {
//       "title": "备案号",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "备案号"
//     },
//     "hideMatching": {
//       "title": "小程序展示额度隐藏匹配产品",
//       "bsonType": "bool",
//       "defaultValue": false,
//       "description": "小程序展示额度隐藏匹配产品"
//     },
//     "gqbg_cover": {
//       "title": "高企报告封面",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "高企报告封面"
//     },
//     "swbg_cover": {
//       "title": "税务报告封面",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "税务报告封面"
//     },
//     "fpbg_cover": {
//       "title": "发票报告封面",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "税务报告封面"
//     },
//     "sqxy": {
//       "title": "授权协议",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "授权协议"
//     },
//     "easyCollect": {
//       "title": "臻企云服精简授权",
//       "bsonType": "bool",
//       "defaultValue": false,
//       "description": "臻企云服精简授权"
//     },
//     "jinrongEduApplyBackimg": {
//       "title": "业务进件背景图图片",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "业务进件背景图图片"
//     },
//     "jinrongEduApplyQrcode": {
//       "title": "业务进件二维码信息",
//       "bsonType": "string",
//       "defaultValue": "",
//       "trim": "both",
//       "description": "业务进件二维码信息"
//     },
//     "jinrongEduShareBackimg": {
//       "title": "业务进件分享图片",
//       "bsonType": "file",
//       "fileMediaType": "image",
//       "fileExtName": "jpg,png",
//       "description": "业务进件分享图片"
//     }
//   }
// }